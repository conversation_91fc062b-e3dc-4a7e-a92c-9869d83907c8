# coding=utf-8
"""
DataFrame processing with referential integrity support.

This module contains the refactored processing functions moved from utility_code.py,
implementing the Protocol-based approach with focus on referential integrity.
"""
import asyncio
from collections import defaultdict
from typing import Dict, List, Any, Optional
from logging import Logger
import pandas as pd
from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import DatabaseContainer
from dags.data_pipeline.database.upsert_operations import upsert_async
from dags.data_pipeline.queue_processors.protocols import (
    DataFrameProcessorProtocol, 
    ReferentialIntegrityProtocol,
    referential_integrity_manager
)

import sqlalchemy.exc
from asyncpg.exceptions import ForeignKeyViolationError as AsyncpgForeignKeyViolationError
from psycopg2.errors import ForeignKeyViolation as Psycopg2ForeignKeyViolation


def _extract_key_info_for_retry(df: pd.DataFrame, model_name: str) -> str:
    """Extract key information from DataFrame for retry logging."""
    key_columns = ["issue_key", "key", "id"]
    available_keys = [col for col in key_columns if col in df.columns]
    
    if not available_keys:
        return f"No key columns found in {model_name}"
    
    key_col = available_keys[0]
    sample_keys = df[key_col].dropna().head(5).tolist()
    return f"{model_name} keys: {sample_keys}"


class DataFrameProcessor:
    """
    Concrete implementation of DataFrame processing with referential integrity.
    
    This class handles the processing of consolidated DataFrames, ensuring that
    parent tables (Issue) are processed before child tables to maintain
    referential integrity.
    """
    
    def __init__(
        self, 
        referential_integrity: Optional[ReferentialIntegrityProtocol] = None,
        logger: Optional[Logger] = None
    ):
        """
        Initialize the DataFrame processor.
        
        Args:
            referential_integrity: Referential integrity coordinator
            logger: Logger instance
        """
        self.referential_integrity = referential_integrity or referential_integrity_manager
        self.logger = logger
    

    async def process_consolidated_dataframes(
        self,
        project_key: str,
        consolidated_dataframes: Dict[str, List[pd.DataFrame]],
        consolidated_configs: Dict[str, Dict[str, Any]],
        logger: Logger,
        message_count: int
    ) -> None:
        """
        Process consolidated DataFrames with referential integrity.
        
        This method ensures that Issue table is processed first, followed by
        child tables in a safe order to maintain referential integrity.
        """
        # Sort to ensure Issue is processed first, then other tables
        sorted_models = sorted(
            consolidated_dataframes.items(), 
            key=lambda x: (x[0] != "Issue", x[0])
        )
        
        for model_name, dataframes in sorted_models:
            logger.debug(f"Processing model: {model_name}")
            
            if not dataframes:
                continue
            
            config = consolidated_configs[model_name]
            model = config["model"]
            
            try:
                # Consolidate DataFrames
                final_df = await self._consolidate_dataframes(dataframes, model_name, logger)
                
                if final_df.empty:
                    continue
                
                logger.debug(f"Processing consolidated {model_name} with {final_df.shape[0]} records")
                
                # Process based on table type
                if model_name == "Issue":
                    await self._process_issue_hierarchy_levels(
                        project_key, final_df, model, config, logger, message_count
                    )
                    # Signal that parent tables are committed
                    await self.referential_integrity.signal_parent_tables_committed()
                else:
                    # For child tables, ensure parent is committed first
                    if self.referential_integrity.is_child_table(model_name):
                        parent_committed = await self.referential_integrity.wait_for_parent_commitment(timeout=30.0)
                        if not parent_committed:
                            logger.warning(f"Proceeding with {model_name} processing despite parent commitment timeout")
                    
                    await self._process_single_model(
                        project_key, final_df, model, config, logger, message_count
                    )
                    
            except Exception as e:
                logger.error(f"Error processing consolidated {model_name}: {e}")
                # Don't raise - allow other models to be processed
    
    async def _consolidate_dataframes(
        self, 
        dataframes: List[pd.DataFrame], 
        model_name: str, 
        logger: Logger
    ) -> pd.DataFrame:
        """
        Consolidate multiple DataFrames into a single DataFrame.
        
        Args:
            dataframes: List of DataFrames to consolidate
            model_name: Name of the model being processed
            logger: Logger instance
            
        Returns:
            Consolidated DataFrame
        """
        if not dataframes:
            return pd.DataFrame()
        
        try:
            # Check for column type consistency
            column_types = defaultdict(set)
            for df in dataframes:
                for col in df.columns:
                    column_types[col].update(df[col].map(type).unique())
            
            # Log columns with mixed types
            for col, types in column_types.items():
                if len(types) > 1:
                    logger.debug(f"Column '{col}' has mixed types: {types}")
            
            # Concatenate DataFrames
            final_df = pd.concat(dataframes, ignore_index=True, sort=False)
            return final_df
            
        except Exception as e:
            logger.error(f"Error consolidating DataFrames for {model_name}: {e}")
            return pd.DataFrame()
    
    @inject
    async def _process_issue_hierarchy_levels(
        self,
        project_key: str,
        df: pd.DataFrame,
        model,
        config: Dict[str, Any],
        logger: Logger,
        message_count: int,
        db_rw_async=Provide[DatabaseContainer.async_session_managers],
    ) -> None:
        """
        Process Issue DataFrame with hierarchy level handling.
        
        This method processes Issues in the correct hierarchy order:
        Initiative (2) -> Epic (1) -> Story (0) -> Subtask (-1)
        """
        # Sort by hierarchy level in descending order
        df = df.sort_values(by="issue_hierarchy_level", ascending=False)
        
        # Define hierarchy mappings
        hierarchy_mappings = {
            2: "Initiative",
            1: "Epic", 
            0: "Story",
            -1: "Subtask"
        }
        
        # Process in specific order to maintain referential integrity
        processing_order = [2, 1, 0, -1]
        
        for hierarchy_level in processing_order:
            level_df = df[df["issue_hierarchy_level"] == hierarchy_level]
            
            if not level_df.empty:
                level_name = hierarchy_mappings[hierarchy_level]
                logger.debug(f"Processing {level_name} (level={hierarchy_level}) with {len(level_df)} records")
                
                try:
                    async with db_rw_async[project_key].async_session() as pg_session:
                        async with pg_session.begin():
                            success = await upsert_async(
                                pg_session,
                                model, level_df,
                                no_update_cols=config["no_update_cols"],
                                on_conflict_update=config["on_conflict_update"],
                                conflict_condition=config["conflict_condition"],
                                message_count=message_count,
                                my_logger=logger
                            )
                            
                            if not success:
                                logger.error(f"Upsert failed for {level_name} batch={message_count}, records={len(level_df)}")
                                raise Exception(f"Upsert failed for {level_name}")
                            
                            logger.debug(f"Successfully processed {level_name} with {len(level_df)} records")
                            
                except Exception as e:
                    logger.error(f"Error processing {level_name}: {e}")
                    raise
    
    @inject
    async def _process_single_model(
        self,
        project_key: str,
        df: pd.DataFrame,
        model,
        config: Dict[str, Any],
        logger: Logger,
        message_count: int,
        db_rw_async=Provide[DatabaseContainer.async_session_managers],
    ) -> None:
        """
        Process a single model DataFrame with standard sorting.
        
        This method handles child tables that depend on the Issue table.
        """
        model_name = model.__name__
        
        # Apply model-specific sorting for child tables
        if model_name in ["ChangelogJSON", "Worklog", "IssueComments", "IssueLinks"]:
            if df.empty:
                return
            df = df.sort_values(by="issue_id", ascending=True)
        
        try:
            async with db_rw_async[project_key].async_session() as pg_session:
                async with pg_session.begin():
                    success = await upsert_async(
                        pg_session,
                        model, df,
                        no_update_cols=config["no_update_cols"],
                        on_conflict_update=config["on_conflict_update"],
                        conflict_condition=config["conflict_condition"],
                        message_count=message_count,
                        my_logger=logger
                    )
                    
                    if not success:
                        logger.error(f"Upsert failed for {model_name} batch={message_count}, records={len(df)}")
                        raise Exception(f"Upsert failed for {model_name}")
                    
                    logger.debug(f"Successfully processed {model_name} with {len(df)} records")
                    
        except sqlalchemy.exc.IntegrityError as e:
            # Handle foreign key violations with proper logging
            if hasattr(e, 'orig') and e.orig:
                if isinstance(e.orig, (AsyncpgForeignKeyViolationError, Psycopg2ForeignKeyViolation)):
                    key_info = _extract_key_info_for_retry(df, model_name)
                    logger.error(f"Foreign key violation in _process_single_model: {key_info}")
                    logger.debug(f"Foreign key violation details: {type(e.orig).__name__}: {e.orig}")
                    raise Exception(f"Foreign key violation for {model_name}")
            raise
        except Exception as e:
            logger.error(f"Error processing {model_name}: {e}")
            raise


# Global instance for application use
dataframe_processor = DataFrameProcessor(logger=None)
